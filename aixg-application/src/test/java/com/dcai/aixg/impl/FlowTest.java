package com.dcai.aixg.impl;

import com.alibaba.fastjson.JSON;
import com.dcai.aixg.AixgApplication;
import com.dcai.aixg.domain.aiagent.flow.FlowService;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.response.ApiResponse;

import static com.ejuetc.commons.base.utils.IOUtils.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileInputStream;
import java.io.FileNotFoundException;

import static com.dcai.aixg.dto.FlowDTO.SrcType.TASK;

/**
 * 工作流测试
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {AixgApplication.class})
public class FlowTest {

    @Autowired
    private FlowService flowService;

    @Test
    public void testValueEvaluation() throws FileNotFoundException {
        ApiResponse<FlowDTO> resp = flowService.launch(new LaunchFlowPO()
                .setSrcType(TASK)
                .setSrcId(System.currentTimeMillis())
                .setConfigCode("VALUE_EVALUATION_FLOW")
                .setRequest(JSON.parseObject(read(new FileInputStream("/Users/<USER>/IdeaProjects/dcai/aixg/docs/prompt/7_价值评测/request.json"))))
                .setRunMode(LaunchFlowPO.RunMode.SYNC)
        );
        System.out.println(JSON.toJSONString(resp, true));
    }

}
